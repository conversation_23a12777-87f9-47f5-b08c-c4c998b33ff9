#!/usr/bin/env python3
"""
خادم API لـ CodeLlama:13b متوافق مع OpenAI API
"""

from flask import Flask, request, jsonify
import requests
import json
import time
import uuid

app = Flask(__name__)

class CodeLlamaServer:
    def __init__(self, ollama_url="http://localhost:11434"):
        self.ollama_url = ollama_url
        self.model = "codellama:13b"
    
    def generate_response(self, messages, temperature=0.2, max_tokens=1000):
        """توليد استجابة من CodeLlama"""
        # تحويل messages إلى prompt واحد
        prompt = ""
        for msg in messages:
            role = msg.get("role", "user")
            content = msg.get("content", "")
            if role == "system":
                prompt += f"System: {content}\n"
            elif role == "user":
                prompt += f"User: {content}\n"
            elif role == "assistant":
                prompt += f"Assistant: {content}\n"
        
        prompt += "Assistant:"
        
        # إرسال الطلب إلى Ollama
        url = f"{self.ollama_url}/api/generate"
        data = {
            "model": self.model,
            "prompt": prompt,
            "stream": False,
            "options": {
                "num_predict": max_tokens,
                "temperature": temperature
            }
        }
        
        try:
            response = requests.post(url, json=data, timeout=60)
            response.raise_for_status()
            return response.json()["response"]
        except Exception as e:
            raise Exception(f"خطأ في Ollama: {e}")

server = CodeLlamaServer()

@app.route('/v1/chat/completions', methods=['POST'])
def chat_completions():
    """نقطة نهاية متوافقة مع OpenAI Chat Completions API"""
    try:
        data = request.json
        messages = data.get('messages', [])
        temperature = data.get('temperature', 0.2)
        max_tokens = data.get('max_tokens', 1000)
        
        # توليد الاستجابة
        response_text = server.generate_response(messages, temperature, max_tokens)
        
        # تنسيق الاستجابة بصيغة OpenAI
        response = {
            "id": f"chatcmpl-{uuid.uuid4().hex[:8]}",
            "object": "chat.completion",
            "created": int(time.time()),
            "model": "codellama:13b",
            "choices": [{
                "index": 0,
                "message": {
                    "role": "assistant",
                    "content": response_text
                },
                "finish_reason": "stop"
            }],
            "usage": {
                "prompt_tokens": len(' '.join([m.get('content', '') for m in messages])) // 4,
                "completion_tokens": len(response_text) // 4,
                "total_tokens": (len(' '.join([m.get('content', '') for m in messages])) + len(response_text)) // 4
            }
        }
        
        return jsonify(response)
        
    except Exception as e:
        return jsonify({"error": str(e)}), 500

@app.route('/v1/completions', methods=['POST'])
def completions():
    """نقطة نهاية متوافقة مع OpenAI Completions API"""
    try:
        data = request.json
        prompt = data.get('prompt', '')
        temperature = data.get('temperature', 0.2)
        max_tokens = data.get('max_tokens', 1000)
        
        # توليد الاستجابة
        messages = [{"role": "user", "content": prompt}]
        response_text = server.generate_response(messages, temperature, max_tokens)
        
        # تنسيق الاستجابة بصيغة OpenAI
        response = {
            "id": f"cmpl-{uuid.uuid4().hex[:8]}",
            "object": "text_completion",
            "created": int(time.time()),
            "model": "codellama:13b",
            "choices": [{
                "text": response_text,
                "index": 0,
                "finish_reason": "stop"
            }],
            "usage": {
                "prompt_tokens": len(prompt) // 4,
                "completion_tokens": len(response_text) // 4,
                "total_tokens": (len(prompt) + len(response_text)) // 4
            }
        }
        
        return jsonify(response)
        
    except Exception as e:
        return jsonify({"error": str(e)}), 500

@app.route('/v1/models', methods=['GET'])
def models():
    """قائمة النماذج المتاحة"""
    return jsonify({
        "object": "list",
        "data": [{
            "id": "codellama:13b",
            "object": "model",
            "created": int(time.time()),
            "owned_by": "local"
        }]
    })

@app.route('/health', methods=['GET'])
def health():
    """فحص صحة الخادم"""
    try:
        # اختبار الاتصال مع Ollama
        response = requests.get(f"{server.ollama_url}/api/tags", timeout=5)
        if response.status_code == 200:
            return jsonify({"status": "healthy", "ollama": "connected"})
        else:
            return jsonify({"status": "unhealthy", "ollama": "disconnected"}), 503
    except:
        return jsonify({"status": "unhealthy", "ollama": "disconnected"}), 503

if __name__ == '__main__':
    print("🦙 تشغيل خادم CodeLlama:13b API...")
    print("📡 العنوان: http://localhost:8001")
    print("📚 نقاط النهاية:")
    print("  - POST /v1/chat/completions")
    print("  - POST /v1/completions") 
    print("  - GET /v1/models")
    print("  - GET /health")
    print("-" * 50)
    
    app.run(host='0.0.0.0', port=8001, debug=False)
