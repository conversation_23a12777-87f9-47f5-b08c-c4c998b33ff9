# دليل استخدام CodeLlama:13b محلياً 🦙

## 🚀 البدء السريع

### الطريقة الأسهل:
```bash
# تشغيل السكريبت التفاعلي
./start_codellama.sh
```

## 📋 الأدوات المتاحة

### 1. **Ollama** (الأداة الرئيسية)
- **الوصف**: أداة سهلة لتشغيل النماذج المحلية
- **المميزات**: 
  - سهولة التثبيت والاستخدام
  - دعم ممتاز لـ CodeLlama
  - API متوافق مع OpenAI
  - إدارة تلقائية للذاكرة

### 2. **العميل التفاعلي Python**
- **الملف**: `codellama_client.py`
- **الاستخدام**: واجهة سطر أوامر تفاعلية
- **الميزات**:
  - توليد كود
  - شرح كود
  - إكمال كود
  - دردشة حول البرمجة

### 3. **خادم API**
- **الملف**: `codellama_server.py`
- **المنفذ**: 8001
- **التوافق**: OpenAI API
- **الاستخدام**: للتكامل مع تطبيقات أخرى

## 🛠️ التثبيت والإعداد

### الخطوة 1: تشغيل Ollama
```bash
# تشغيل خادم Ollama
./bin/ollama serve
```

### الخطوة 2: تحميل CodeLlama:13b
```bash
# في طرفية جديدة
./bin/ollama pull codellama:13b
```

### الخطوة 3: اختبار النموذج
```bash
./bin/ollama run codellama:13b
```

## 💻 أمثلة الاستخدام

### استخدام مباشر مع Ollama:
```bash
# تشغيل تفاعلي
./bin/ollama run codellama:13b

# مثال على prompt
>>> Write a Python function to calculate factorial
```

### استخدام العميل Python:
```bash
python3 codellama_client.py

# أمثلة على الأوامر:
> /generate اكتب دالة Python لحساب الفيبوناتشي
> /explain def factorial(n): return 1 if n <= 1 else n * factorial(n-1)
> /complete def bubble_sort(arr):
```

### استخدام API:
```python
import requests

# طلب إكمال كود
response = requests.post('http://localhost:8001/v1/chat/completions', json={
    "model": "codellama:13b",
    "messages": [
        {"role": "user", "content": "Write a Python function to reverse a string"}
    ],
    "temperature": 0.2
})

print(response.json()['choices'][0]['message']['content'])
```

## 🔧 التكامل مع محررات الكود

### VS Code مع Continue.dev:
1. ثبت Continue extension
2. أضف إلى `~/.continue/config.json`:
```json
{
  "models": [
    {
      "title": "CodeLlama 13B Local",
      "provider": "ollama", 
      "model": "codellama:13b",
      "apiBase": "http://localhost:11434"
    }
  ]
}
```

### Windsurf:
يمكن استخدام خادم API المحلي:
```json
{
  "codeium.api.url": "http://localhost:8001",
  "codeium.model": "codellama:13b"
}
```

## ⚡ نصائح للأداء

### 1. **تحسين الذاكرة**:
```bash
# تعيين حد الذاكرة لـ Ollama
export OLLAMA_MAX_LOADED_MODELS=1
export OLLAMA_MAX_QUEUE=512
```

### 2. **تحسين المعالج**:
```bash
# استخدام جميع النوى
export OLLAMA_NUM_PARALLEL=4
```

### 3. **تحسين GPU** (إذا متوفر):
```bash
# تفعيل دعم GPU
export OLLAMA_GPU_LAYERS=35
```

## 🎯 حالات الاستخدام المثلى

### ✅ ممتاز لـ:
- **إكمال الكود**: اقتراحات ذكية أثناء الكتابة
- **شرح الكود**: فهم الكود المعقد
- **مراجعة الكود**: اكتشاف الأخطاء والتحسينات
- **توليد الاختبارات**: كتابة unit tests
- **التوثيق**: إنشاء تعليقات وdocstrings

### ⚠️ محدود في:
- **المشاريع الضخمة**: قد يحتاج context أكبر
- **اللغات النادرة**: أفضل مع Python, JavaScript, C++
- **المنطق المعقد**: قد يحتاج مراجعة بشرية

## 🔍 استكشاف الأخطاء

### مشكلة: Ollama لا يعمل
```bash
# التحقق من العملية
ps aux | grep ollama

# إعادة تشغيل
pkill ollama
./bin/ollama serve
```

### مشكلة: النموذج بطيء
```bash
# تحقق من استخدام الذاكرة
htop

# قلل max_tokens في الطلبات
# استخدم temperature أقل (0.1-0.3)
```

### مشكلة: خطأ في الاتصال
```bash
# تحقق من المنفذ
netstat -tlnp | grep 11434

# تحقق من الجدار الناري
sudo ufw status
```

## 📊 مقارنة الأدوات

| الأداة | السهولة | الأداء | التكامل | الميزات |
|--------|---------|--------|----------|---------|
| Ollama | ⭐⭐⭐⭐⭐ | ⭐⭐⭐⭐ | ⭐⭐⭐⭐ | ⭐⭐⭐⭐ |
| Python Client | ⭐⭐⭐⭐ | ⭐⭐⭐ | ⭐⭐⭐ | ⭐⭐⭐⭐⭐ |
| API Server | ⭐⭐⭐ | ⭐⭐⭐⭐ | ⭐⭐⭐⭐⭐ | ⭐⭐⭐⭐ |

## 🚀 الخطوات التالية

1. **جرب النموذج**: ابدأ بأمثلة بسيطة
2. **اختبر الأداء**: قس السرعة على جهازك
3. **ادمج مع محررك**: استخدم Continue أو extension مشابه
4. **طور workflow**: أنشئ عملية عمل مخصصة
5. **شارك التجربة**: ساعد المجتمع بتجربتك

## 📞 الدعم

في حالة وجود مشاكل:
1. تحقق من سجلات Ollama
2. راجع استخدام الموارد
3. جرب إعادة تشغيل النظام
4. تحقق من إصدار Ollama

---

**ملاحظة**: CodeLlama:13b يحتاج حوالي 8GB من الذاكرة للعمل بكفاءة.
