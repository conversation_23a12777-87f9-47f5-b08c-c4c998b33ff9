#!/usr/bin/env python3
"""
إعداد Windsurf للعمل مع CodeLlama:13b محلياً
"""

import json
import os
import requests
import subprocess
from pathlib import Path

class WindsurfCodeLlamaSetup:
    def __init__(self):
        self.windsurf_config_dir = Path.home() / ".config" / "Windsurf" / "User"
        self.settings_file = self.windsurf_config_dir / "settings.json"
        self.ollama_url = "http://localhost:11434"
        self.local_server_url = "http://localhost:8001"
        
    def check_ollama_status(self):
        """التحقق من حالة Ollama"""
        try:
            response = requests.get(f"{self.ollama_url}/api/tags", timeout=5)
            return response.status_code == 200
        except:
            return False
    
    def start_ollama(self):
        """تشغيل Ollama"""
        print("🚀 تشغيل Ollama...")
        try:
            subprocess.Popen(["./bin/ollama", "serve"], 
                           cwd="/home/<USER>/.local/share/codegeex-models")
            print("✅ تم تشغيل Ollama")
            return True
        except Exception as e:
            print(f"❌ فشل في تشغيل Ollama: {e}")
            return False
    
    def check_codellama_model(self):
        """التحقق من وجود نموذج CodeLlama"""
        try:
            response = requests.get(f"{self.ollama_url}/api/tags")
            models = response.json().get("models", [])
            return any("codellama:13b" in model.get("name", "") for model in models)
        except:
            return False
    
    def download_codellama(self):
        """تحميل نموذج CodeLlama"""
        print("📥 تحميل CodeLlama:13b...")
        print("⚠️  هذا قد يستغرق وقتاً طويلاً (حوالي 7GB)")
        
        try:
            result = subprocess.run([
                "./bin/ollama", "pull", "codellama:13b"
            ], cwd="/home/<USER>/.local/share/codegeex-models", 
               capture_output=True, text=True)
            
            if result.returncode == 0:
                print("✅ تم تحميل CodeLlama بنجاح!")
                return True
            else:
                print(f"❌ فشل في التحميل: {result.stderr}")
                return False
        except Exception as e:
            print(f"❌ خطأ في التحميل: {e}")
            return False
    
    def create_windsurf_settings(self):
        """إنشاء إعدادات Windsurf للعمل مع CodeLlama"""
        
        # إنشاء المجلد إذا لم يكن موجوداً
        self.windsurf_config_dir.mkdir(parents=True, exist_ok=True)
        
        # قراءة الإعدادات الحالية أو إنشاء جديدة
        current_settings = {}
        if self.settings_file.exists():
            try:
                with open(self.settings_file, 'r', encoding='utf-8') as f:
                    current_settings = json.load(f)
            except:
                current_settings = {}
        
        # إعدادات CodeLlama للـ Windsurf
        codellama_settings = {
            # إعدادات عامة
            "editor.inlineSuggest.enabled": True,
            "editor.suggest.preview": True,
            "editor.quickSuggestions": {
                "other": True,
                "comments": True,
                "strings": True
            },
            
            # إعدادات AI مخصصة (إذا كانت متاحة)
            "ai.model.provider": "custom",
            "ai.model.endpoint": self.local_server_url,
            "ai.model.name": "codellama:13b",
            "ai.model.temperature": 0.2,
            "ai.model.maxTokens": 2048,
            
            # إعدادات للتكامل مع خادم محلي
            "http.proxy": "",
            "http.proxyStrictSSL": False,
            
            # تعليقات للمستخدم
            "// CodeLlama Setup": "تم إعداد CodeLlama:13b محلياً",
            "// Local Server": f"الخادم المحلي: {self.local_server_url}",
            "// Ollama Server": f"خادم Ollama: {self.ollama_url}"
        }
        
        # دمج الإعدادات
        current_settings.update(codellama_settings)
        
        # حفظ الإعدادات
        try:
            with open(self.settings_file, 'w', encoding='utf-8') as f:
                json.dump(current_settings, f, indent=2, ensure_ascii=False)
            print(f"✅ تم حفظ إعدادات Windsurf في: {self.settings_file}")
            return True
        except Exception as e:
            print(f"❌ فشل في حفظ الإعدادات: {e}")
            return False
    
    def start_local_server(self):
        """تشغيل الخادم المحلي للتكامل مع Windsurf"""
        print("🚀 تشغيل الخادم المحلي...")
        try:
            subprocess.Popen([
                "python3", "codellama_server.py"
            ], cwd="/home/<USER>/.local/share/codegeex-models")
            print(f"✅ الخادم المحلي يعمل على: {self.local_server_url}")
            return True
        except Exception as e:
            print(f"❌ فشل في تشغيل الخادم: {e}")
            return False
    
    def test_setup(self):
        """اختبار الإعداد"""
        print("🧪 اختبار الإعداد...")
        
        # اختبار Ollama
        if self.check_ollama_status():
            print("✅ Ollama يعمل")
        else:
            print("❌ Ollama لا يعمل")
            return False
        
        # اختبار النموذج
        if self.check_codellama_model():
            print("✅ CodeLlama:13b متاح")
        else:
            print("❌ CodeLlama:13b غير متاح")
            return False
        
        # اختبار الخادم المحلي
        try:
            response = requests.get(f"{self.local_server_url}/health", timeout=5)
            if response.status_code == 200:
                print("✅ الخادم المحلي يعمل")
            else:
                print("❌ الخادم المحلي لا يعمل")
        except:
            print("⚠️  الخادم المحلي غير متاح (سيتم تشغيله)")
        
        return True
    
    def setup_complete(self):
        """تشغيل الإعداد الكامل"""
        print("🦙 إعداد CodeLlama:13b مع Windsurf")
        print("=" * 40)
        
        # 1. التحقق من Ollama
        if not self.check_ollama_status():
            if not self.start_ollama():
                return False
            import time
            time.sleep(3)  # انتظار تشغيل Ollama
        
        # 2. التحقق من النموذج
        if not self.check_codellama_model():
            if not self.download_codellama():
                return False
        
        # 3. إنشاء إعدادات Windsurf
        if not self.create_windsurf_settings():
            return False
        
        # 4. تشغيل الخادم المحلي
        self.start_local_server()
        
        # 5. اختبار الإعداد
        import time
        time.sleep(2)
        self.test_setup()
        
        print("\n🎉 تم الإعداد بنجاح!")
        print("\n📋 الخطوات التالية:")
        print("1. أعد تشغيل Windsurf")
        print("2. جرب كتابة كود واستخدم اقتراحات AI")
        print("3. استخدم الخادم المحلي للدردشة")
        print(f"4. الخادم متاح على: {self.local_server_url}")
        
        return True

def main():
    setup = WindsurfCodeLlamaSetup()
    
    print("🦙 مرحباً بك في إعداد CodeLlama مع Windsurf!")
    print("\nالخيارات المتاحة:")
    print("1. إعداد كامل (موصى به)")
    print("2. تشغيل Ollama فقط")
    print("3. تحميل CodeLlama فقط")
    print("4. إنشاء إعدادات Windsurf فقط")
    print("5. اختبار الإعداد الحالي")
    print("6. خروج")
    
    while True:
        choice = input("\nاختر رقماً (1-6): ").strip()
        
        if choice == "1":
            setup.setup_complete()
            break
        elif choice == "2":
            setup.start_ollama()
        elif choice == "3":
            setup.download_codellama()
        elif choice == "4":
            setup.create_windsurf_settings()
        elif choice == "5":
            setup.test_setup()
        elif choice == "6":
            print("👋 وداعاً!")
            break
        else:
            print("❌ خيار غير صحيح")

if __name__ == "__main__":
    main()
