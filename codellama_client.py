#!/usr/bin/env python3
"""
عميل Python لاستخدام CodeLlama:13b محلياً عبر Ollama
"""

import requests
import json
import sys

class CodeLlamaClient:
    def __init__(self, base_url="http://localhost:11434"):
        self.base_url = base_url
        self.model = "codellama:13b"
    
    def generate_code(self, prompt, max_tokens=1000):
        """توليد كود باستخدام CodeLlama"""
        url = f"{self.base_url}/api/generate"
        
        data = {
            "model": self.model,
            "prompt": prompt,
            "stream": False,
            "options": {
                "num_predict": max_tokens,
                "temperature": 0.2
            }
        }
        
        try:
            response = requests.post(url, json=data)
            response.raise_for_status()
            return response.json()["response"]
        except requests.exceptions.RequestException as e:
            return f"خطأ في الاتصال: {e}"
    
    def chat_with_code(self, message):
        """دردشة مع CodeLlama حول الكود"""
        prompt = f"You are a helpful coding assistant. Answer in Arabic when possible.\n\nUser: {message}\nAssistant:"
        return self.generate_code(prompt)
    
    def explain_code(self, code):
        """شرح الكود"""
        prompt = f"Explain this code in Arabic:\n\n```\n{code}\n```\n\nExplanation:"
        return self.generate_code(prompt)
    
    def complete_code(self, partial_code):
        """إكمال الكود"""
        prompt = f"Complete this code:\n\n{partial_code}"
        return self.generate_code(prompt)

def main():
    client = CodeLlamaClient()
    
    print("🦙 مرحباً بك في CodeLlama:13b المحلي!")
    print("الأوامر المتاحة:")
    print("  /generate <prompt> - توليد كود")
    print("  /explain <code> - شرح كود")
    print("  /complete <partial_code> - إكمال كود")
    print("  /quit - خروج")
    print("-" * 50)
    
    while True:
        try:
            user_input = input("\n> ").strip()
            
            if user_input == "/quit":
                print("وداعاً! 👋")
                break
            
            elif user_input.startswith("/generate "):
                prompt = user_input[10:]
                result = client.generate_code(prompt)
                print(f"\n📝 النتيجة:\n{result}")
            
            elif user_input.startswith("/explain "):
                code = user_input[9:]
                result = client.explain_code(code)
                print(f"\n💡 الشرح:\n{result}")
            
            elif user_input.startswith("/complete "):
                partial_code = user_input[10:]
                result = client.complete_code(partial_code)
                print(f"\n✅ الكود المكتمل:\n{result}")
            
            else:
                result = client.chat_with_code(user_input)
                print(f"\n🤖 CodeLlama:\n{result}")
                
        except KeyboardInterrupt:
            print("\n\nوداعاً! 👋")
            break
        except Exception as e:
            print(f"\nخطأ: {e}")

if __name__ == "__main__":
    main()
