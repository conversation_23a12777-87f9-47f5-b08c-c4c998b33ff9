# دليل استخدام CodeLlama:13b مع Windsurf 🦙

## ⚠️ المشكلة الحالية
Continue.dev **غير متوافق مع Windsurf** - يعمل فقط مع VS Code العادي.

## 🔧 الحلول المتاحة

### الحل 1: استخدام VS Code العادي مع Continue.dev ⭐⭐⭐⭐⭐
```bash
# تشغيل VS Code العادي
code .

# Continue.dev مُعد بالفعل ويعمل مع CodeLlama:13b
# اضغط Ctrl+L للدردشة
```

### الحل 2: إعداد Windsurf مع خادم محلي ⭐⭐⭐⭐
```bash
# تشغيل الإعداد التلقائي
python3 windsurf_codellama_setup.py

# اختر "1" للإعداد الكامل
```

### الحل 3: استخدام العميل التفاعلي ⭐⭐⭐⭐⭐
```bash
# تشغيل العميل المباشر
./start_codellama.sh

# اختر "1" للعميل التفاعلي
```

### الحل 4: استخدام خادم API مع أي محرر ⭐⭐⭐⭐
```bash
# تشغيل خادم API
python3 codellama_server.py

# استخدم API من أي تطبيق
curl -X POST http://localhost:8001/v1/chat/completions \
  -H "Content-Type: application/json" \
  -d '{
    "model": "codellama:13b",
    "messages": [{"role": "user", "content": "اكتب دالة Python"}]
  }'
```

## 🚀 التوصية الأفضل

### للاستخدام الفوري:
1. **استخدم VS Code العادي** مع Continue.dev (مُعد بالفعل)
2. **أو استخدم العميل التفاعلي** `./start_codellama.sh`

### للاستخدام مع Windsurf:
1. **شغل الخادم المحلي** `python3 codellama_server.py`
2. **استخدم API calls** من داخل Windsurf
3. **أو استخدم العميل في طرفية منفصلة**

## 📋 خطوات التشغيل السريع

### الطريقة 1: VS Code + Continue.dev
```bash
# 1. تشغيل Ollama
./bin/ollama serve

# 2. في طرفية جديدة - تحميل النموذج
./bin/ollama pull codellama:13b

# 3. تشغيل VS Code
code .

# 4. في VS Code اضغط Ctrl+L للدردشة
```

### الطريقة 2: العميل التفاعلي
```bash
# تشغيل النظام الكامل
./start_codellama.sh

# اختر "1" للعميل التفاعلي
# اكتب أسئلتك مباشرة
```

### الطريقة 3: خادم API + Windsurf
```bash
# 1. تشغيل Ollama
./bin/ollama serve &

# 2. تحميل النموذج
./bin/ollama pull codellama:13b

# 3. تشغيل خادم API
python3 codellama_server.py &

# 4. استخدم من Windsurf أو أي محرر
```

## 🔧 إعداد Windsurf (اختياري)

### إعداد تلقائي:
```bash
python3 windsurf_codellama_setup.py
```

### إعداد يدوي:
1. **أضف إلى إعدادات Windsurf** (`~/.config/Windsurf/User/settings.json`):
```json
{
  "ai.model.provider": "custom",
  "ai.model.endpoint": "http://localhost:8001",
  "ai.model.name": "codellama:13b"
}
```

2. **أعد تشغيل Windsurf**

## 🎯 أمثلة الاستخدام

### مع VS Code + Continue.dev:
1. افتح VS Code: `code .`
2. اضغط `Ctrl+L`
3. اكتب: "اكتب دالة Python لحساب الفيبوناتشي"

### مع العميل التفاعلي:
```bash
./start_codellama.sh
# اختر 1
> /generate اكتب دالة Python لحساب الفيبوناتشي
```

### مع API:
```python
import requests

response = requests.post('http://localhost:8001/v1/chat/completions', json={
    "model": "codellama:13b",
    "messages": [{"role": "user", "content": "اكتب دالة Python لحساب الفيبوناتشي"}]
})

print(response.json()['choices'][0]['message']['content'])
```

## 🔍 استكشاف الأخطاء

### مشكلة: Ollama لا يعمل
```bash
# تحقق من العملية
ps aux | grep ollama

# إعادة تشغيل
pkill ollama
./bin/ollama serve
```

### مشكلة: النموذج غير موجود
```bash
# تحقق من النماذج
./bin/ollama list

# تحميل النموذج
./bin/ollama pull codellama:13b
```

### مشكلة: Continue.dev لا يعمل في Windsurf
**هذا طبيعي!** Continue.dev مصمم لـ VS Code فقط.
**الحل**: استخدم VS Code العادي أو الخادم المحلي.

## 📊 مقارنة الحلول

| الحل | السهولة | الميزات | التوافق | الأداء |
|------|---------|---------|----------|---------|
| VS Code + Continue | ⭐⭐⭐⭐⭐ | ⭐⭐⭐⭐⭐ | ⭐⭐⭐⭐⭐ | ⭐⭐⭐⭐ |
| العميل التفاعلي | ⭐⭐⭐⭐⭐ | ⭐⭐⭐⭐ | ⭐⭐⭐⭐⭐ | ⭐⭐⭐⭐ |
| خادم API | ⭐⭐⭐ | ⭐⭐⭐⭐ | ⭐⭐⭐⭐⭐ | ⭐⭐⭐⭐ |
| Windsurf مُعدل | ⭐⭐ | ⭐⭐⭐ | ⭐⭐⭐ | ⭐⭐⭐ |

## 🎉 التوصية النهائية

**للبدء الفوري**: استخدم `./start_codellama.sh` واختر العميل التفاعلي

**للتطوير المتقدم**: استخدم VS Code مع Continue.dev (مُعد بالفعل)

**للتكامل مع Windsurf**: استخدم خادم API المحلي
