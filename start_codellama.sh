#!/bin/bash

# سكريبت تشغيل CodeLlama:13b محلياً

echo "🦙 مرحباً بك في CodeLlama:13b المحلي!"
echo "=================================="

# التحقق من وجود Ollama
if [ ! -f "./bin/ollama" ]; then
    echo "❌ Ollama غير موجود. يرجى تثبيته أولاً."
    exit 1
fi

# دالة للتحقق من تشغيل Ollama
check_ollama() {
    if pgrep -f "ollama serve" > /dev/null; then
        return 0
    else
        return 1
    fi
}

# دالة لتشغيل Ollama
start_ollama() {
    echo "🚀 تشغيل خادم Ollama..."
    ./bin/ollama serve &
    OLLAMA_PID=$!
    
    # انتظار تشغيل الخادم
    echo "⏳ انتظار تشغيل الخادم..."
    sleep 5
    
    # التحقق من التشغيل
    if check_ollama; then
        echo "✅ خادم Ollama يعمل بنجاح!"
        return 0
    else
        echo "❌ فشل في تشغيل خادم Ollama"
        return 1
    fi
}

# دالة لتحميل النموذج
download_model() {
    echo "📥 تحميل نموذج CodeLlama:13b..."
    echo "⚠️  هذا قد يستغرق وقتاً طويلاً (حوالي 7GB)"
    
    ./bin/ollama pull codellama:13b
    
    if [ $? -eq 0 ]; then
        echo "✅ تم تحميل النموذج بنجاح!"
        return 0
    else
        echo "❌ فشل في تحميل النموذج"
        return 1
    fi
}

# دالة للتحقق من وجود النموذج
check_model() {
    ./bin/ollama list | grep -q "codellama:13b"
    return $?
}

# التحقق من تشغيل Ollama
if ! check_ollama; then
    start_ollama
    if [ $? -ne 0 ]; then
        exit 1
    fi
else
    echo "✅ خادم Ollama يعمل بالفعل"
fi

# التحقق من وجود النموذج
if ! check_model; then
    echo "📦 النموذج غير موجود، سيتم تحميله..."
    download_model
    if [ $? -ne 0 ]; then
        exit 1
    fi
else
    echo "✅ نموذج CodeLlama:13b موجود"
fi

echo ""
echo "🎉 CodeLlama:13b جاهز للاستخدام!"
echo ""
echo "📋 الخيارات المتاحة:"
echo "1. تشغيل العميل التفاعلي"
echo "2. تشغيل خادم API"
echo "3. تشغيل Ollama مباشرة"
echo "4. اختبار النموذج"
echo "5. خروج"
echo ""

while true; do
    read -p "اختر رقماً (1-5): " choice
    
    case $choice in
        1)
            echo "🚀 تشغيل العميل التفاعلي..."
            python3 codellama_client.py
            ;;
        2)
            echo "🚀 تشغيل خادم API على المنفذ 8001..."
            python3 codellama_server.py
            ;;
        3)
            echo "🚀 تشغيل Ollama مباشرة..."
            ./bin/ollama run codellama:13b
            ;;
        4)
            echo "🧪 اختبار النموذج..."
            echo "def fibonacci(n):" | ./bin/ollama run codellama:13b
            ;;
        5)
            echo "👋 وداعاً!"
            # إيقاف Ollama إذا كان يعمل
            if [ ! -z "$OLLAMA_PID" ]; then
                kill $OLLAMA_PID 2>/dev/null
            fi
            exit 0
            ;;
        *)
            echo "❌ خيار غير صحيح. يرجى اختيار رقم من 1 إلى 5."
            ;;
    esac
    
    echo ""
    echo "📋 الخيارات المتاحة:"
    echo "1. تشغيل العميل التفاعلي"
    echo "2. تشغيل خادم API"
    echo "3. تشغيل Ollama مباشرة"
    echo "4. اختبار النموذج"
    echo "5. خروج"
    echo ""
done
